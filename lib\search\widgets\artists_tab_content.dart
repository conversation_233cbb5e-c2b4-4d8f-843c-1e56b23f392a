import 'dart:developer';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/providers/search_artists_data_provider.dart';
import 'package:portraitmode/search/providers/search_artists_provider.dart';

class ArtistsTabContent extends ConsumerStatefulWidget {
  const ArtistsTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
  });

  final ArtistsSearchData searchData;
  final String keyword;
  final List<ArtistData> dataList;

  @override
  ArtistsTabContentState createState() => ArtistsTabContentState();
}

class ArtistsTabContentState extends ConsumerState<ArtistsTabContent> {
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _scrollController = ScrollController();
  late final int _profileId;

  final _artistListService = ArtistListService();
  final int _loadMorePerPage = 20;
  bool _isLoadingMore = false;

  double? _loadMoreThreshold;

  @override
  void initState() {
    super.initState();

    _profileId = LocalUserService.userId ?? 0;
    _scrollController.addListener(_onScroll);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_canLoadMore()) _triggerLoadMore();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_canLoadMore()) _triggerLoadMore();
  }

  double _getLoadMoreThreshold() {
    if (_loadMoreThreshold == null) {
      final screenHeight = MediaQuery.of(context).size.height;
      final dynamicThreshold = screenHeight * LoadMoreConfig.tresholdByScreen;

      // Add bounds by min and max.
      _loadMoreThreshold = math.max(
        math.min(dynamicThreshold, LoadMoreConfig.maxTreshold),
        LoadMoreConfig.minTreshold,
      );
    }
    return _loadMoreThreshold!;
  }

  bool _canLoadMore() {
    return _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent -
                _getLoadMoreThreshold() &&
        !_isLoadingMore &&
        !widget.searchData.loadMoreEndReached;
  }

  void _triggerLoadMore() async {
    if (_isLoadingMore || widget.searchData.loadMoreEndReached) return;

    if (mounted) {
      setState(() {
        _isLoadingMore = true;
      });
    }

    await _handleLoadMore();

    if (mounted) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  double? _cacheExtent;

  @override
  Widget build(BuildContext context) {
    // log('build screen: ArtistsTabContent');
    log('The artists search keyword param is: "${widget.keyword}"');

    if (_cacheExtent == null) {
      final double screenHeight = MediaQuery.sizeOf(context).height;
      _cacheExtent = screenHeight * 2.5;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 768.0),
      child: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _handleRefresh,
        child: CustomScrollView(
          controller: _scrollController,
          cacheExtent: _cacheExtent,
          slivers: [
            SliverOverlapInjector(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  // Handle loading indicator as the last item
                  if (index == widget.dataList.length) {
                    return Container(
                      padding: const EdgeInsets.all(16.0),
                      alignment: Alignment.center,
                      child: CircularProgressIndicator(
                        color: context.colors.baseColorAlt,
                      ),
                    );
                  }

                  final int artistId = widget.dataList[index].id;

                  return Padding(
                    key: ValueKey(artistId),
                    padding: EdgeInsets.only(
                      left: ScreenStyleConfig.horizontalPadding,
                      right: ScreenStyleConfig.horizontalPadding,
                      top: index == 0 ? LayoutConfig.contentTopGap : 12.0,
                    ),
                    child: ArtistListItem(
                      index: index,
                      artist: widget.dataList[index],
                      isOwnProfile: artistId == _profileId,
                    ),
                  );
                },
                childCount:
                    widget.dataList.length +
                    (_isLoadingMore && !widget.searchData.loadMoreEndReached
                        ? 1
                        : 0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // Reset loading state
    _isLoadingMore = false;

    late ArtistListResponse response;

    // log('keyword onRefresh: ${searchData.keyword}');

    if (widget.keyword.isNotEmpty) {
      response = await _artistListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: -1,
        lastTotalPhotos: -1,
      );
    } else {
      response = await _artistListService.fetch(
        limit: _loadMorePerPage,
        lastId: -1,
        lastTotalPhotos: -1,
      );
    }

    _handleArtistListResponse(response, true, false);
  }

  Future<void> _handleLoadMore() async {
    late ArtistListResponse response;

    log('The artists search keyword is: "${widget.keyword}"');

    if (widget.keyword.isNotEmpty) {
      response = await _artistListService.search(
        keyword: widget.keyword,
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
        lastTotalPhotos: widget.searchData.lastTotalPhotos,
      );
    } else {
      response = await _artistListService.fetch(
        limit: _loadMorePerPage,
        lastId: widget.searchData.loadMoreLastId,
        lastTotalPhotos: widget.searchData.lastTotalPhotos,
      );
    }

    final isFirstLoad = widget.searchData.loadMoreLastId == -1;

    _handleArtistListResponse(response, false, isFirstLoad);
  }

  void _handleArtistListResponse(
    ArtistListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    // In case user navigates back immediately before async request complete.
    if (!mounted) return;

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    final searchArtistsReactiveService = ref.read(
      searchArtistsReactiveServiceProvider,
    );

    if (response.data.isEmpty) {
      if (isRefresh) {
        searchArtistsReactiveService.clear();
      }

      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    ref
        .read(artistStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    ref
        .read(searchArtistsDataProvider.notifier)
        .updateSomeValues(
          loadMoreLastId: response.data.last.id,
          lastTotalPhotos: response.data.last.totalPhotos,
        );

    if (isRefresh) {
      searchArtistsReactiveService.replaceAll(response.data);
    } else {
      if (isFirstLoad) {
        searchArtistsReactiveService.replaceAll(response.data);
      } else {
        searchArtistsReactiveService.addItems(response.data);
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchArtistsDataProvider.notifier).setLoadMoreEndReached(false);
    }
  }
}
